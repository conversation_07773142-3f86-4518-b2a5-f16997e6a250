from PIL import Image
import json

# 检查图像信息
image_path = r"E:\xuewei\手掌\IMG_1980.JPG"
json_path = r"E:\xuewei\手掌\IMG_1980.json"

# 获取图像尺寸
with Image.open(image_path) as img:
    img_width, img_height = img.size
    print(f"图像尺寸: {img_width} x {img_height}")

# 读取JSON文件
with open(json_path, 'r', encoding='utf-8') as f:
    data = json.load(f)

print("\n边界框信息:")
for shape in data['shapes']:
    if shape['shape_type'] == 'rectangle':
        points = shape['points']
        x1, y1 = points[0]
        x2, y2 = points[1]
        print(f"边界框: ({x1}, {y1}) -> ({x2}, {y2})")
        
        # 计算边界框尺寸
        width = abs(x2 - x1)
        height = abs(y2 - y1)
        print(f"边界框尺寸: {width} x {height}")
        
        # 计算中心点
        x_center = (x1 + x2) / 2
        y_center = (y1 + y2) / 2
        print(f"中心点: ({x_center}, {y_center})")
        
        # 归一化坐标
        x_center_norm = x_center / img_width
        y_center_norm = y_center / img_height
        width_norm = width / img_width
        height_norm = height / img_height
        
        print(f"归一化中心点: ({x_center_norm:.6f}, {y_center_norm:.6f})")
        print(f"归一化尺寸: ({width_norm:.6f}, {height_norm:.6f})")

print("\n关键点信息:")
for shape in data['shapes']:
    if shape['shape_type'] == 'point':
        x, y = shape['points'][0]
        x_norm = x / img_width
        y_norm = y / img_height
        print(f"{shape['label']}: ({x}, {y}) -> 归一化: ({x_norm:.6f}, {y_norm:.6f})")
        
        # 检查是否超出范围
        if x_norm > 1.0 or y_norm > 1.0:
            print(f"  ⚠️ 警告: {shape['label']} 的归一化坐标超出范围!")
