import json
import os
from PIL import Image
import glob

def fix_coordinates_in_json(json_file_path):
    """
    修复JSON文件中超出图像边界的坐标
    """
    with open(json_file_path, 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    # 获取图像尺寸
    image_path = data['imagePath']
    json_dir = os.path.dirname(json_file_path)
    full_image_path = os.path.join(json_dir, image_path)
    
    if not os.path.exists(full_image_path):
        print(f"警告: 找不到图像文件 {full_image_path}")
        return False
    
    try:
        with Image.open(full_image_path) as img:
            img_width, img_height = img.size
    except Exception as e:
        print(f"错误: 无法读取图像 {full_image_path}: {e}")
        return False
    
    print(f"处理文件: {json_file_path}")
    print(f"图像尺寸: {img_width} x {img_height}")
    
    fixed = False
    
    # 检查并修复所有形状的坐标
    for shape in data['shapes']:
        if shape['shape_type'] == 'point':
            # 处理关键点
            x, y = shape['points'][0]
            original_x, original_y = x, y
            
            # 限制坐标在图像边界内
            x = max(0, min(x, img_width - 1))
            y = max(0, min(y, img_height - 1))
            
            if x != original_x or y != original_y:
                print(f"  修复关键点 {shape['label']}: ({original_x}, {original_y}) -> ({x}, {y})")
                shape['points'][0] = [x, y]
                fixed = True
                
        elif shape['shape_type'] == 'rectangle':
            # 处理边界框
            points = shape['points']
            x1, y1 = points[0]
            x2, y2 = points[1]
            original_points = [[x1, y1], [x2, y2]]
            
            # 限制坐标在图像边界内
            x1 = max(0, min(x1, img_width - 1))
            y1 = max(0, min(y1, img_height - 1))
            x2 = max(0, min(x2, img_width - 1))
            y2 = max(0, min(y2, img_height - 1))
            
            new_points = [[x1, y1], [x2, y2]]
            
            if new_points != original_points:
                print(f"  修复边界框 {shape['label']}: {original_points} -> {new_points}")
                shape['points'] = new_points
                fixed = True
    
    # 如果有修复，保存文件
    if fixed:
        # 创建备份
        backup_path = json_file_path + '.backup'
        if not os.path.exists(backup_path):
            import shutil
            shutil.copy2(json_file_path, backup_path)
            print(f"  创建备份: {backup_path}")
        
        # 保存修复后的文件
        with open(json_file_path, 'w', encoding='utf-8') as f:
            json.dump(data, f, indent=2, ensure_ascii=False)
        print(f"  已保存修复后的文件")
        return True
    else:
        print(f"  无需修复")
        return False

def batch_fix_coordinates(input_dir):
    """
    批量修复目录中所有JSON文件的坐标
    """
    json_files = glob.glob(os.path.join(input_dir, '*.json'))
    
    if not json_files:
        print(f"在 {input_dir} 中没有找到JSON文件")
        return
    
    fixed_count = 0
    total_count = len(json_files)
    
    print(f"找到 {total_count} 个JSON文件，开始检查和修复...")
    print("=" * 50)
    
    for json_file in json_files:
        try:
            if fix_coordinates_in_json(json_file):
                fixed_count += 1
            print("-" * 30)
        except Exception as e:
            print(f"处理失败 {json_file}: {e}")
            print("-" * 30)
    
    print(f"\n修复完成: {fixed_count}/{total_count} 个文件被修复")

if __name__ == "__main__":
    # 修复单个文件
    single_file = r"E:\xuewei\手掌\IMG_1980.json"
    print("修复单个文件:")
    fix_coordinates_in_json(single_file)
    
    print("\n" + "=" * 60)
    
    # 批量修复（可选）
    input_directory = r"E:\xuewei\手掌"
    print("批量检查所有文件:")
    batch_fix_coordinates(input_directory)
